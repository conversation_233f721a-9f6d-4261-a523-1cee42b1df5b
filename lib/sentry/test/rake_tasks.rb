# frozen_string_literal: true

require "rake/clean"
require "rspec/core/rake_task"

module Sentry
  module Test
    module RakeTasks
      extend Rake::DS<PERSON>

      def self.define_spec_tasks(options = {})
        opts = {
          isolated_specs_pattern: "spec/isolated/**/*_spec.rb",
          spec_pattern: nil,
          spec_exclude_pattern: nil,
          spec_rspec_opts: nil,
          isolated_rspec_opts: nil
        }.merge(options)

        RSpec::Core::RakeTask.new(:spec).tap do |task|
          task.pattern = opts[:spec_pattern] if opts[:spec_pattern]
          task.exclude_pattern = opts[:spec_exclude_pattern] if opts[:spec_exclude_pattern]
          task.rspec_opts = opts[:spec_rspec_opts] if opts[:spec_rspec_opts]
        end

        namespace :spec do
          RSpec::Core::RakeTask.new(:isolated).tap do |task|
            task.pattern = opts[:isolated_specs_pattern]
            task.rspec_opts = opts[:isolated_rspec_opts] if opts[:isolated_rspec_opts]
          end

          # Coverage-enabled spec tasks
          RSpec::Core::RakeTask.new(:coverage).tap do |task|
            task.pattern = opts[:spec_pattern] if opts[:spec_pattern]
            task.exclude_pattern = opts[:spec_exclude_pattern] if opts[:spec_exclude_pattern]
            rspec_opts = [opts[:spec_rspec_opts], "--require simplecov"].compact.join(" ")
            task.rspec_opts = rspec_opts
          end

          desc "Run specs with coverage and open coverage report"
          task :coverage_open => :coverage do
            coverage_path = File.join(Dir.pwd, "coverage", "index.html")
            if File.exist?(coverage_path)
              puts "Opening coverage report at #{coverage_path}"
              system("open #{coverage_path}") || system("xdg-open #{coverage_path}")
            else
              puts "Coverage report not found at #{coverage_path}"
            end
          end

          desc "Run specific spec pattern with coverage (usage: rake spec:coverage_for[pattern])"
          task :coverage_for, [:pattern] do |t, args|
            pattern = args[:pattern] || "spec/**/*_spec.rb"
            puts "Running specs matching pattern: #{pattern}"

            # Use Dir.glob to expand the pattern and pass individual files
            spec_files = Dir.glob(pattern)
            if spec_files.empty?
              puts "No spec files found matching pattern: #{pattern}"
              next
            end

            puts "Found #{spec_files.length} spec file(s)"
            sh "bundle exec rspec #{spec_files.join(' ')} --require simplecov --format progress"

            coverage_path = File.join(Dir.pwd, "coverage", "index.html")
            if File.exist?(coverage_path)
              puts "\nCoverage report generated at #{coverage_path}"
              puts "To open: rake spec:coverage_open"
            end
          end

          desc "Run specs for specific directory with coverage (usage: rake spec:coverage_dir[directory])"
          task :coverage_dir, [:directory] do |t, args|
            directory = args[:directory] || "spec"
            pattern = "#{directory}/**/*_spec.rb"
            puts "Running specs in directory: #{directory}"

            # Use Dir.glob to expand the pattern and pass individual files
            spec_files = Dir.glob(pattern)
            if spec_files.empty?
              puts "No spec files found in directory: #{directory}"
              next
            end

            puts "Found #{spec_files.length} spec file(s)"
            sh "bundle exec rspec #{spec_files.join(' ')} --require simplecov --format progress"

            coverage_path = File.join(Dir.pwd, "coverage", "index.html")
            if File.exist?(coverage_path)
              puts "\nCoverage report generated at #{coverage_path}"
              puts "To open: rake spec:coverage_open"
            end
          end
        end
      end

      # Define versioned specs task (sentry-rails specific)
      def self.define_versioned_specs_task(options = {})
        opts = {
          rspec_opts: "--order rand"
        }.merge(options)

        namespace :spec do
          RSpec::Core::RakeTask.new(:versioned).tap do |task|
            ruby_ver_dir = RUBY_VERSION.split(".")[0..1].join(".")
            matching_dir = Dir["spec/versioned/*"].detect { |dir| File.basename(dir) <= ruby_ver_dir }

            unless matching_dir
              puts "No versioned specs found for ruby #{RUBY_VERSION}"
              exit 0
            end

            puts "Running versioned specs from #{matching_dir} for ruby #{RUBY_VERSION}"

            task.rspec_opts = opts[:rspec_opts]
            task.pattern = "#{matching_dir}/**/*_spec.rb"
          end
        end
      end
    end
  end
end
