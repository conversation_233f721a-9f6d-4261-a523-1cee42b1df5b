# frozen_string_literal: true

require "rake/clean"
require "rspec/core/rake_task"

module Sentry
  module Test
    module RakeTasks
      extend Rake::DS<PERSON>

      def self.define_spec_tasks(options = {})
        opts = {
          isolated_specs_pattern: "spec/isolated/**/*_spec.rb",
          spec_pattern: nil,
          spec_exclude_pattern: nil,
          spec_rspec_opts: nil,
          isolated_rspec_opts: nil
        }.merge(options)

        RSpec::Core::RakeTask.new(:spec).tap do |task|
          task.pattern = opts[:spec_pattern] if opts[:spec_pattern]
          task.exclude_pattern = opts[:spec_exclude_pattern] if opts[:spec_exclude_pattern]
          task.rspec_opts = opts[:spec_rspec_opts] if opts[:spec_rspec_opts]
        end

        namespace :spec do
          RSpec::Core::RakeTask.new(:isolated).tap do |task|
            task.pattern = opts[:isolated_specs_pattern]
            task.rspec_opts = opts[:isolated_rspec_opts] if opts[:isolated_rspec_opts]
          end

          # Coverage-enabled spec tasks
          RSpec::Core::RakeTask.new(:coverage).tap do |task|
            task.pattern = opts[:spec_pattern] if opts[:spec_pattern]
            task.exclude_pattern = opts[:spec_exclude_pattern] if opts[:spec_exclude_pattern]
            rspec_opts = [opts[:spec_rspec_opts], "--require simplecov"].compact.join(" ")
            task.rspec_opts = rspec_opts
          end

          desc "Run specs with coverage and open coverage report"
          task :coverage_open => :coverage do
            coverage_path = File.join(Dir.pwd, "coverage", "index.html")
            if File.exist?(coverage_path)
              puts "Opening coverage report at #{coverage_path}"
              system("open #{coverage_path}") || system("xdg-open #{coverage_path}")
            else
              puts "Coverage report not found at #{coverage_path}"
            end
          end

          # Coverage for specific directories
          desc "Run log subscriber specs with coverage"
          RSpec::Core::RakeTask.new(:log_subscribers_coverage).tap do |task|
            task.pattern = "spec/sentry/rails/log_subscribers/**/*_spec.rb"
            task.rspec_opts = "--require simplecov --format progress"
          end
        end
      end

      # Define versioned specs task (sentry-rails specific)
      def self.define_versioned_specs_task(options = {})
        opts = {
          rspec_opts: "--order rand"
        }.merge(options)

        namespace :spec do
          RSpec::Core::RakeTask.new(:versioned).tap do |task|
            ruby_ver_dir = RUBY_VERSION.split(".")[0..1].join(".")
            matching_dir = Dir["spec/versioned/*"].detect { |dir| File.basename(dir) <= ruby_ver_dir }

            unless matching_dir
              puts "No versioned specs found for ruby #{RUBY_VERSION}"
              exit 0
            end

            puts "Running versioned specs from #{matching_dir} for ruby #{RUBY_VERSION}"

            task.rspec_opts = opts[:rspec_opts]
            task.pattern = "#{matching_dir}/**/*_spec.rb"
          end
        end
      end
    end
  end
end
