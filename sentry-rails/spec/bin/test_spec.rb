# frozen_string_literal: true

require 'spec_helper'
require 'tempfile'
require 'fileutils'

# Load the RailsVersionTester class from bin/test
load File.expand_path('../../bin/test', __dir__)

RSpec.describe RailsVersionTester do
  let(:tester) { described_class.new }
  let(:temp_dir) { Dir.mktmpdir }

  before do
    # Change to temp directory for isolated testing
    @original_dir = Dir.pwd
    Dir.chdir(temp_dir)

    # Create a basic Gemfile for testing
    File.write('Gemfile', <<~GEMFILE)
      source 'https://rubygems.org'
      gem 'rails', ENV['RAILS_VERSION'] || '~> 7.0'
      gem 'rspec'
    GEMFILE
  end

  after do
    Dir.chdir(@original_dir)
    FileUtils.rm_rf(temp_dir)
  end

  describe '#run' do
    context 'with --help option' do
      it 'displays help information' do
        expect { tester.run(['--help']) }.to output(/Rails Version Tester for sentry-rails/).to_stdout
      end
    end

    context 'with --list option' do
      it 'lists supported Rails versions' do
        expect { tester.run(['--list']) }.to output(/Supported Rails versions:/).to_stdout
      end

      it 'shows lock file status for each version' do
        output = capture_stdout { tester.run(['--list']) }
        expect(output).to include('(no lock file)')
      end
    end

    context 'with --version option' do
      it 'accepts valid Rails versions' do
        allow(tester).to receive(:test_rails_version).and_return(0)
        expect { tester.run(['--version', '7.0']) }.not_to raise_error
      end

      it 'rejects invalid Rails versions' do
        expect { tester.run(['--version', '99.0']) }.to raise_error(SystemExit)
      end

      it 'shows error message for invalid versions' do
        expect do
          begin
            tester.run(['--version', '99.0'])
          rescue SystemExit
          end
        end.to output(/Error: Unsupported Rails version '99.0'/).to_stdout
      end
    end

    context 'with no arguments' do
      it 'shows error message' do
        expect do
          begin
            tester.run([])
          rescue SystemExit
          end
        end.to output(/Error: No action specified/).to_stdout
      end
    end
  end

  describe '#generate_lock_file_name' do
    it 'generates correct lock file name for Ruby/Rails combination' do
      # Use the actual Ruby version from the tester instance
      ruby_version = tester.instance_variable_get(:@ruby_version)

      lock_file = tester.send(:generate_lock_file_name, '7.0')
      expect(lock_file).to eq("Gemfile-ruby-#{ruby_version}-rails-7.0.lock")
    end

    it 'handles version strings with extra characters' do
      # Mock the ruby version for this specific test
      tester.instance_variable_set(:@ruby_version, '3.0.0-p123')

      lock_file = tester.send(:generate_lock_file_name, '7.0.1')
      expect(lock_file).to eq('Gemfile-ruby-3.0.0-rails-7.0.1.lock')
    end
  end

  describe '#setup_lock_file' do
    let(:dedicated_lock_file) { 'Gemfile-ruby-3.0.0-rails-7.0.lock' }
    let(:current_lock_file) { 'Gemfile.lock' }

    context 'when dedicated lock file exists' do
      before do
        File.write(dedicated_lock_file, 'dedicated lock content')
      end

      it 'copies dedicated lock file to current location' do
        tester.send(:setup_lock_file, dedicated_lock_file, current_lock_file)
        expect(File.read(current_lock_file)).to eq('dedicated lock content')
      end
    end

    context 'when no dedicated lock file exists but current one does' do
      before do
        File.write(current_lock_file, 'current lock content')
      end

      it 'removes existing lock file for fresh resolution' do
        tester.send(:setup_lock_file, dedicated_lock_file, current_lock_file)
        expect(File.exist?(current_lock_file)).to be false
      end
    end
  end

  describe '#save_lock_file' do
    let(:dedicated_lock_file) { 'Gemfile-ruby-3.0.0-rails-7.0.lock' }
    let(:current_lock_file) { 'Gemfile.lock' }

    context 'when current lock file exists' do
      before do
        File.write(current_lock_file, 'current lock content')
      end

      it 'saves current lock file to dedicated location' do
        tester.send(:save_lock_file, dedicated_lock_file, current_lock_file)
        expect(File.read(dedicated_lock_file)).to eq('current lock content')
      end
    end
  end

  describe '#bundle_update_needed?' do
    let(:env) { { 'RAILS_VERSION' => '7.0', 'BUNDLE_GEMFILE' => 'Gemfile' } }
    let(:dedicated_lock_file) { 'Gemfile-ruby-3.0.0-rails-7.0.lock' }

    context 'when no lock file exists' do
      it 'returns true' do
        expect(tester.send(:bundle_update_needed?, env, dedicated_lock_file)).to be true
      end
    end

    context 'when Gemfile is newer than lock file' do
      before do
        File.write('Gemfile.lock', 'old content')
        sleep(0.1) # Ensure time difference
        File.write('Gemfile', 'new content')
      end

      it 'returns true' do
        expect(tester.send(:bundle_update_needed?, env, dedicated_lock_file)).to be true
      end
    end
  end

  describe '#lockfile_has_incompatible_rails_version?' do
    let(:lockfile_path) { 'test.lock' }

    context 'with compatible Rails version' do
      before do
        File.write(lockfile_path, <<~LOCKFILE)
          GEM
            remote: https://rubygems.org/
            specs:
              rails (7.0.4)
        LOCKFILE
      end

      it 'returns false for same major.minor version' do
        result = tester.send(:lockfile_has_incompatible_rails_version?, lockfile_path, '7.0')
        expect(result).to be false
      end
    end

    context 'with incompatible Rails version' do
      before do
        File.write(lockfile_path, <<~LOCKFILE)
          GEM
            remote: https://rubygems.org/
            specs:
              rails (6.1.4)
        LOCKFILE
      end

      it 'returns true for different major.minor version' do
        result = tester.send(:lockfile_has_incompatible_rails_version?, lockfile_path, '7.0')
        expect(result).to be true
      end
    end
  end

  describe '#clean_lock_files' do
    before do
      # Use the actual Ruby version from the tester instance
      ruby_version = tester.instance_variable_get(:@ruby_version)
      # Create some test lock files with the actual Ruby version
      File.write("Gemfile-ruby-#{ruby_version}-rails-6.1.lock", 'content')
      File.write("Gemfile-ruby-#{ruby_version}-rails-7.0.lock", 'content')
    end

    it 'finds matching lock files' do
      allow($stdin).to receive(:gets).and_return("n\n")

      expect { tester.send(:clean_lock_files) }.to output(/Found 2 lock file/).to_stdout
    end
  end

  describe '#update_bundle' do
    let(:env) { { 'RAILS_VERSION' => '7.0' } }
    let(:dedicated_lock_file) { 'Gemfile-ruby-3.0.0-rails-7.0.lock' }

    context 'when bundle update succeeds' do
      it 'returns true' do
        allow(tester).to receive(:system).with(env, 'bundle update --quiet').and_return(true)

        result = tester.send(:update_bundle, env, dedicated_lock_file)
        expect(result).to be true
      end
    end

    context 'when bundle update fails but install succeeds' do
      it 'tries bundle install and returns true' do
        allow(tester).to receive(:system).with(env, 'bundle update --quiet').and_return(false)
        allow(tester).to receive(:system).with(env, 'bundle install --quiet').and_return(true)
        allow(File).to receive(:exist?).with('Gemfile.lock').and_return(true)
        allow(File).to receive(:delete)

        result = tester.send(:update_bundle, env, dedicated_lock_file)
        expect(result).to be true
      end
    end

    context 'when both bundle update and install fail' do
      it 'returns false' do
        allow(tester).to receive(:system).with(env, 'bundle update --quiet').and_return(false)
        allow(tester).to receive(:system).with(env, 'bundle install --quiet').and_return(false)
        allow(File).to receive(:exist?).with('Gemfile.lock').and_return(true)
        allow(File).to receive(:delete)

        result = tester.send(:update_bundle, env, dedicated_lock_file)
        expect(result).to be false
      end
    end
  end

  describe '#run_tests' do
    let(:env) { { 'RAILS_VERSION' => '7.0' } }

    context 'with no spec paths' do
      it 'runs all tests via rake' do
        allow(tester).to receive(:spawn).and_return(123)
        allow(Process).to receive(:wait2).with(123).and_return([nil, double(exitstatus: 0)])

        result = tester.send(:run_tests, env, [])
        expect(result).to eq(0)
      end
    end

    context 'with specific spec paths' do
      it 'runs specific specs via rspec' do
        allow(tester).to receive(:spawn).and_return(123)
        allow(Process).to receive(:wait2).with(123).and_return([nil, double(exitstatus: 0)])

        result = tester.send(:run_tests, env, ['spec/some_spec.rb'])
        expect(result).to eq(0)
      end
    end

    context 'when interrupted' do
      it 'handles interruption gracefully' do
        allow(tester).to receive(:spawn).and_return(123)
        allow(Process).to receive(:wait2).with(123).and_raise(Interrupt)
        allow(tester).to receive(:terminate_process_group)

        result = tester.send(:run_tests, env, [])
        expect(result).to eq(130)
      end
    end
  end

  describe '#terminate_process_group' do
    let(:pid) { 123 }

    context 'when process exists' do
      it 'sends TERM signal to process group' do
        allow(Process).to receive(:kill).with('TERM', -pid)
        allow(tester).to receive(:sleep)
        allow(tester).to receive(:process_running?).with(pid).and_return(false)

        expect(Process).to receive(:kill).with('TERM', -pid)
        tester.send(:terminate_process_group, pid)
      end
    end

    context 'when process is still running after TERM' do
      it 'sends KILL signal' do
        allow(Process).to receive(:kill).with('TERM', -pid)
        allow(tester).to receive(:sleep)
        allow(tester).to receive(:process_running?).with(pid).and_return(true)

        expect(Process).to receive(:kill).with('KILL', -pid)
        tester.send(:terminate_process_group, pid)
      end
    end

    context 'when process does not exist' do
      it 'handles ESRCH error gracefully' do
        allow(Process).to receive(:kill).with('TERM', -pid).and_raise(Errno::ESRCH)

        expect { tester.send(:terminate_process_group, pid) }.not_to raise_error
      end
    end
  end

  describe '#process_running?' do
    let(:pid) { 123 }

    context 'when process exists' do
      it 'returns true' do
        allow(Process).to receive(:getpgid).with(pid).and_return(123)

        result = tester.send(:process_running?, pid)
        expect(result).to be true
      end
    end

    context 'when process does not exist' do
      it 'returns false' do
        allow(Process).to receive(:getpgid).with(pid).and_raise(Errno::ESRCH)

        result = tester.send(:process_running?, pid)
        expect(result).to be false
      end
    end
  end

  describe '#test_all_versions' do
    before do
      allow(tester).to receive(:test_rails_version).and_return(0)
    end

    it 'tests all supported versions' do
      described_class::SUPPORTED_VERSIONS.each do |version|
        expect(tester).to receive(:test_rails_version).with(version).and_return(0)
      end

      expect { tester.send(:test_all_versions) }.to raise_error(SystemExit) do |error|
        expect(error.status).to eq(0)
      end
    end

    context 'when some versions fail' do
      before do
        # Set up specific return values for each version
        described_class::SUPPORTED_VERSIONS.each do |version|
          if version == '5.0'
            allow(tester).to receive(:test_rails_version).with(version).and_return(1)
          else
            allow(tester).to receive(:test_rails_version).with(version).and_return(0)
          end
        end
      end

      it 'tracks failed versions and exits with error code' do
        expect { tester.send(:test_all_versions) }.to raise_error(SystemExit) do |error|
          expect(error.status).to eq(1)
        end
      end
    end
  end

  describe '#test_single_version' do
    context 'when test passes' do
      it 'does not exit' do
        allow(tester).to receive(:test_rails_version).with('7.0').and_return(0)

        expect { tester.send(:test_single_version, '7.0') }.not_to raise_error
      end
    end

    context 'when test fails' do
      it 'exits with error code' do
        allow(tester).to receive(:test_rails_version).with('7.0').and_return(1)

        expect { tester.send(:test_single_version, '7.0') }.to raise_error(SystemExit) do |error|
          expect(error.status).to eq(1)
        end
      end
    end
  end

  private

  def capture_stdout
    original_stdout = $stdout
    $stdout = StringIO.new
    yield
    $stdout.string
  ensure
    $stdout = original_stdout
  end
end
